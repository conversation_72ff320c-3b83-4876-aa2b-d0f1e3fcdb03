# SketchUp Drag & Drop Feature for KH-Tools

## Overview
This feature adds drag and drop functionality for SketchUp (.skp) files in Blender using the KH-Tools addon.

## Features
- **Drag & Drop Import**: Simply drag .skp files from your file explorer and drop them into Blender
- **Automatic Processing**: Files are automatically imported using KH-Tools SketchUp importer with optimal settings
- **Multiple Drop Zones**: Works in 3D Viewport, Outliner, and Properties panels
- **Status Panel**: Visual feedback showing drag & drop readiness

## How to Use

### Method 1: Drag & Drop (New Feature)
1. Open Blender with KH-Tools addon enabled
2. Open your file explorer and navigate to your .skp files
3. Drag a .skp file from the file explorer
4. Drop it onto any of these areas in Blender:
   - 3D Viewport
   - Outliner panel
   - Properties panel
5. Import settings dialog will open automatically
6. Configure your import settings as needed
7. Click "Import SKP" to complete the import

### Method 2: Traditional Import (Existing)
1. Go to File > Import > KH-Import Sketchup (.skp)
2. Browse and select your .skp file
3. Click Import

## Status Panel
A new "Drag & Drop" panel is available under the SketchUp Manager in the KH-Tools category:
- Shows current drag & drop status
- Provides usage instructions
- Lists supported drop zones

## Technical Details

### Files Added
- `skp_drag_drop.py`: Main drag & drop functionality
- Integration with main `__init__.py` file

### Classes Implemented
- `SKP_Drop_Importer`: Operator that handles the import process
- `SKP_FileDropHandler`: File handler that enables drag & drop for .skp files
- `SKP_DragDropPanel`: UI panel showing status and instructions

### Import Settings Dialog
When files are dropped, the standard KH-Tools import dialog opens with these configurable options:
- **Last View In SketchUp As Camera View**: Import camera from SketchUp
- **Use Existing Materials**: Reuse materials already in the scene
- **Instancing Threshold**: Maximum number of similar objects before instancing
- **Instancing Type**: Choose between Faces or Vertices for instancing
- **Import A Scene**: Additional scene import options
- **Default Shading**: Choose shading mode (Cycles, etc.)

You can adjust these settings before clicking "Import SKP"

## Requirements
- Blender 4.0+
- KH-Tools addon enabled
- SketchUp import functionality available

## Troubleshooting

### "KH TOOLS SketchUp importer is not available"
- Make sure KH-Tools addon is properly installed and enabled
- Check that the SketchUp import functionality is working through the traditional File > Import menu

### Files not importing
- Ensure the file has a .skp extension
- Verify the file is a valid SketchUp file
- Check the Blender console for error messages

### Drag & drop not working
- Make sure you're dropping files in supported areas (3D Viewport, Outliner, Properties)
- Try restarting Blender if the feature stops working

## Benefits
- **Faster Workflow**: No need to navigate through File > Import menus
- **Settings Control**: Full access to import settings via dialog
- **Intuitive**: Works like standard file operations in other software
- **Integrated**: Uses existing KH-Tools import dialog and processing
- **Flexible**: Configure settings per import operation

## Future Enhancements
- Support for multiple file selection
- Custom import settings per drag & drop operation
- Progress indicators for large files
- Batch processing with progress feedback

---
*This feature is part of the KH-Tools addon for Blender*
