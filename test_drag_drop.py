"""
Test script for SketchUp Drag & Drop functionality
Run this script in Blender to test the drag & drop feature
"""

import bpy
import os

def test_drag_drop_functionality():
    """Test the drag & drop functionality"""
    print("=" * 50)
    print("Testing SketchUp Drag & Drop Functionality")
    print("=" * 50)
    
    # Test 1: Check if operator is registered
    print("\n1. Checking operator registration...")
    if hasattr(bpy.ops.wm, 'skp_drop_importer'):
        print("   ✓ SKP Drop Importer operator is registered")
    else:
        print("   ✗ SKP Drop Importer operator not found")
        return False
    
    # Test 2: Check if KH-Tools importer is available
    print("\n2. Checking KH-Tools SketchUp importer...")
    if hasattr(bpy.ops.import_scene, 'skp_k'):
        print("   ✓ KH-Tools SketchUp importer is available")
    else:
        print("   ✗ KH-Tools SketchUp importer not found")
        print("   Please make sure KH-Tools addon is enabled")
        return False
    
    # Test 3: Check file handler registration
    print("\n3. Checking file handler registration...")
    file_handlers = []
    try:
        for handler_class in bpy.types.FileHandler.__subclasses__():
            if hasattr(handler_class, 'bl_idname') and handler_class.bl_idname == "SKP_FH_import":
                file_handlers.append(handler_class)
        
        if file_handlers:
            print("   ✓ SKP File Handler is registered")
        else:
            print("   ✗ SKP File Handler not found")
    except Exception as e:
        print(f"   ! Error checking file handlers: {e}")
    
    # Test 4: Check panel registration
    print("\n4. Checking panel registration...")
    panels = []
    try:
        for panel_class in bpy.types.Panel.__subclasses__():
            if hasattr(panel_class, 'bl_idname') and panel_class.bl_idname == "VIEW3D_PT_skp_drag_drop":
                panels.append(panel_class)
        
        if panels:
            print("   ✓ SKP Drag Drop Panel is registered")
        else:
            print("   ✗ SKP Drag Drop Panel not found")
    except Exception as e:
        print(f"   ! Error checking panels: {e}")
    
    # Test 5: Test operator with invalid file (should handle gracefully)
    print("\n5. Testing operator error handling...")
    try:
        result = bpy.ops.wm.skp_drop_importer(filepath="nonexistent.skp")
        print(f"   ✓ Operator handled invalid file gracefully: {result}")
    except Exception as e:
        print(f"   ! Operator error handling: {e}")
    
    print("\n" + "=" * 50)
    print("Test completed!")
    print("=" * 50)
    
    # Instructions for manual testing
    print("\nMANUAL TESTING INSTRUCTIONS:")
    print("1. Find a .skp file on your computer")
    print("2. Open file explorer and navigate to the .skp file")
    print("3. Drag the .skp file from file explorer")
    print("4. Drop it onto the 3D Viewport in Blender")
    print("5. Import settings dialog should open")
    print("6. Configure settings and click 'Import SKP'")
    print("\nSupported drop zones:")
    print("- 3D Viewport")
    print("- Outliner panel")
    print("- Properties panel")
    
    return True

def show_drag_drop_panel_info():
    """Show information about the drag & drop panel"""
    print("\n" + "=" * 50)
    print("DRAG & DROP PANEL INFORMATION")
    print("=" * 50)
    
    print("\nTo find the Drag & Drop panel:")
    print("1. Go to the 3D Viewport")
    print("2. Press 'N' to open the side panel")
    print("3. Look for the 'KH-Tools' tab")
    print("4. Find 'Sketchup Manager' section")
    print("5. Look for 'Drag & Drop' sub-panel")
    
    # Check if SketchUp Manager is enabled
    try:
        prefs = bpy.context.preferences.addons['KH-Tools'].preferences
        if hasattr(prefs, 'KH_Sketchup') and prefs.KH_Sketchup:
            print("\n✓ SketchUp Manager is enabled in preferences")
        else:
            print("\n✗ SketchUp Manager is disabled in preferences")
            print("  Enable it in: Edit > Preferences > Add-ons > KH-Tools")
    except:
        print("\n! Could not check SketchUp Manager preferences")

if __name__ == "__main__":
    # Run the tests
    success = test_drag_drop_functionality()
    
    if success:
        show_drag_drop_panel_info()
    else:
        print("\n! Some tests failed. Please check the KH-Tools addon installation.")
